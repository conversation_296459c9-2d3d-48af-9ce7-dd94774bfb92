#!/usr/bin/env python3
"""
Test script to validate the improved clean.py logic
Compares original vs improved signal detection
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def load_data():
    """Load the trading data"""
    try:
        df = pd.read_csv('HIFIUSDT_20250913_14_02_45.csv')
        print(f"Loaded {len(df)} rows of data")
        return df
    except FileNotFoundError:
        print("Error: Could not find data file")
        return None

def original_logic(df):
    """Original signal detection logic"""
    BUY_018_orig = []
    SELL_018_orig = []
    b_list_018_orig = []
    s_list_018_orig = []
    position_018_orig = False
    
    for idx, row in df.iterrows():
        i = df.index.get_loc(idx)
        
        if i == 0:
            BUY_018_orig.append(np.nan)
            b_list_018_orig.append(0)
            SELL_018_orig.append(np.nan)
            s_list_018_orig.append(0)
            continue
        
        prev_row = df.iloc[i - 1]

        if not position_018_orig:
            # BUY conditions
            if (
                row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
                and row['P_PCT_017'] > 0
                and row['CLOSE'] > prev_row['CLOSE']
            ):
                BUY_018_orig.append(row['CLOSE'])
                b_list_018_orig.append(row['CLOSE'])
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)
                position_018_orig = True

            elif (
                row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
                and row['Long_Min_017'] < -1
                and (row['P_PCT_017'] - row['Long_Min_017']) > 1.5
                and row['CLOSE'] > prev_row['CLOSE']
            ):
                BUY_018_orig.append(row['CLOSE'])
                b_list_018_orig.append(row['CLOSE'])
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)
                position_018_orig = True

            else:
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)

        else:
            # SELL conditions
            if (
                row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
                and row['P_PCT_017'] > 0
                and row['CLOSE'] < prev_row['CLOSE']
            ):
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(row['CLOSE'])
                s_list_018_orig.append(row['CLOSE'])
                position_018_orig = False

            elif (
                row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
                and row['Short_Min_017'] < -1
                and (row['P_PCT_017'] - row['Short_Min_017']) > 1.5
                and row['CLOSE'] < prev_row['CLOSE']
            ):
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(row['CLOSE'])
                s_list_018_orig.append(row['CLOSE'])
                position_018_orig = False

            else:
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)
    
    return BUY_018_orig, SELL_018_orig, b_list_018_orig, s_list_018_orig

def analyze_signals(df, buy_signals, sell_signals, label=""):
    """Analyze signal quality and performance"""
    buy_count = sum(1 for x in buy_signals if not pd.isna(x))
    sell_count = sum(1 for x in sell_signals if not pd.isna(x))
    
    print(f"\n{label} Analysis:")
    print(f"Buy signals: {buy_count}")
    print(f"Sell signals: {sell_count}")
    print(f"Signal balance: {abs(buy_count - sell_count)}")
    
    # Calculate potential returns
    if buy_count > 0 and sell_count > 0:
        buy_prices = [x for x in buy_signals if not pd.isna(x)]
        sell_prices = [x for x in sell_signals if not pd.isna(x)]
        
        # Match buy/sell pairs
        pairs = min(len(buy_prices), len(sell_prices))
        if pairs > 0:
            total_return = 0
            for i in range(pairs):
                if i < len(sell_prices):
                    trade_return = (sell_prices[i] - buy_prices[i]) / buy_prices[i] * 100
                    total_return += trade_return
            
            avg_return = total_return / pairs
            print(f"Average return per trade: {avg_return:.2f}%")
            print(f"Total return: {total_return:.2f}%")
    
    return buy_count, sell_count

def main():
    """Main test function"""
    print("Testing Clean.py Logic Improvements")
    print("=" * 50)
    
    # Load data
    df = load_data()
    if df is None:
        return
    
    # Test original logic
    print("\nTesting Original Logic...")
    buy_orig, sell_orig, b_list_orig, s_list_orig = original_logic(df.copy())
    analyze_signals(df, buy_orig, sell_orig, "Original Logic")
    
    # Test improved logic by running the clean.py file
    print("\nTesting Improved Logic...")
    test_005 = df.copy()  # Create the expected variable name
    
    # Execute the improved logic
    exec(open('clean.py').read())
    
    # Analyze improved results
    analyze_signals(df, test_005['Buy_S_P_018'], test_005['Sell_S_P_018'], "Improved Logic")
    
    # Compare flag transitions
    print("\nFlag Transition Analysis:")
    flag_changes = 0
    b_to_s = 0
    s_to_b = 0
    
    for i in range(1, len(df)):
        if df.iloc[i]['Flag_017'] != df.iloc[i-1]['Flag_017']:
            flag_changes += 1
            if df.iloc[i-1]['Flag_017'] == 'B' and df.iloc[i]['Flag_017'] == 'S':
                b_to_s += 1
            elif df.iloc[i-1]['Flag_017'] == 'S' and df.iloc[i]['Flag_017'] == 'B':
                s_to_b += 1
    
    print(f"Total flag changes: {flag_changes}")
    print(f"B->S transitions: {b_to_s}")
    print(f"S->B transitions: {s_to_b}")
    
    # Show sample of results
    print("\nSample Results (first 20 signals):")
    signal_cols = ['TIME', 'Flag_017', 'P_PCT_017', 'CLOSE', 'Buy_S_P_018', 'Sell_S_P_018']
    sample_df = test_005[signal_cols].head(50)
    
    # Filter to show only rows with signals
    has_signal = (
        ~pd.isna(sample_df['Buy_S_P_018']) | 
        ~pd.isna(sample_df['Sell_S_P_018'])
    )
    
    if has_signal.any():
        print(sample_df[has_signal].head(10).to_string(index=False))
    else:
        print("No signals found in first 50 rows")

if __name__ == "__main__":
    main()
