#!/usr/bin/env python3
"""
Comprehensive comparison of all signal detection versions
"""

import pandas as pd
import numpy as np

def load_data():
    """Load the trading data"""
    try:
        df = pd.read_csv('HIFIUSDT_20250913_14_02_45.csv')
        print(f"Loaded {len(df)} rows of data")
        return df
    except FileNotFoundError:
        print("Error: Could not find data file")
        return None

def calculate_performance_metrics(buy_signals, sell_signals, label=""):
    """Calculate comprehensive performance metrics"""
    buy_count = sum(1 for x in buy_signals if not pd.isna(x))
    sell_count = sum(1 for x in sell_signals if not pd.isna(x))
    
    metrics = {
        'label': label,
        'buy_signals': buy_count,
        'sell_signals': sell_count,
        'signal_balance': abs(buy_count - sell_count),
        'avg_return': 0,
        'total_return': 0,
        'win_rate': 0,
        'max_drawdown': 0
    }
    
    if buy_count > 0 and sell_count > 0:
        buy_prices = [x for x in buy_signals if not pd.isna(x)]
        sell_prices = [x for x in sell_signals if not pd.isna(x)]
        
        pairs = min(len(buy_prices), len(sell_prices))
        if pairs > 0:
            returns = []
            winning_trades = 0
            
            for i in range(pairs):
                if i < len(sell_prices):
                    trade_return = (sell_prices[i] - buy_prices[i]) / buy_prices[i] * 100
                    returns.append(trade_return)
                    if trade_return > 0:
                        winning_trades += 1
            
            metrics['avg_return'] = sum(returns) / len(returns)
            metrics['total_return'] = sum(returns)
            metrics['win_rate'] = winning_trades / len(returns) * 100
            
            # Calculate max drawdown
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = cumulative_returns - running_max
            metrics['max_drawdown'] = np.min(drawdown)
    
    return metrics

def run_original_logic(df):
    """Run original signal detection logic"""
    BUY_018_orig = []
    SELL_018_orig = []
    b_list_018_orig = []
    s_list_018_orig = []
    position_018_orig = False
    
    for idx, row in df.iterrows():
        i = df.index.get_loc(idx)
        
        if i == 0:
            BUY_018_orig.append(np.nan)
            b_list_018_orig.append(0)
            SELL_018_orig.append(np.nan)
            s_list_018_orig.append(0)
            continue
        
        prev_row = df.iloc[i - 1]

        if not position_018_orig:
            if (
                row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
                and row['P_PCT_017'] > 0
                and row['CLOSE'] > prev_row['CLOSE']
            ):
                BUY_018_orig.append(row['CLOSE'])
                b_list_018_orig.append(row['CLOSE'])
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)
                position_018_orig = True

            elif (
                row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
                and row['Long_Min_017'] < -1
                and (row['P_PCT_017'] - row['Long_Min_017']) > 1.5
                and row['CLOSE'] > prev_row['CLOSE']
            ):
                BUY_018_orig.append(row['CLOSE'])
                b_list_018_orig.append(row['CLOSE'])
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)
                position_018_orig = True

            else:
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)

        else:
            if (
                row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
                and row['P_PCT_017'] > 0
                and row['CLOSE'] < prev_row['CLOSE']
            ):
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(row['CLOSE'])
                s_list_018_orig.append(row['CLOSE'])
                position_018_orig = False

            elif (
                row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
                and row['Short_Min_017'] < -1
                and (row['P_PCT_017'] - row['Short_Min_017']) > 1.5
                and row['CLOSE'] < prev_row['CLOSE']
            ):
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(row['CLOSE'])
                s_list_018_orig.append(row['CLOSE'])
                position_018_orig = False

            else:
                BUY_018_orig.append(np.nan)
                b_list_018_orig.append(0)
                SELL_018_orig.append(np.nan)
                s_list_018_orig.append(0)
    
    return BUY_018_orig, SELL_018_orig

def run_improved_logic(df):
    """Run improved signal detection logic"""
    test_005 = df.copy()
    exec(open('clean.py').read())
    return test_005['Buy_S_P_018'].tolist(), test_005['Sell_S_P_018'].tolist()

def run_optimized_logic(df):
    """Run optimized signal detection logic"""
    test_005 = df.copy()
    exec(open('clean_optimized.py').read())
    return test_005['Buy_S_P_018'].tolist(), test_005['Sell_S_P_018'].tolist()

def print_comparison_table(metrics_list):
    """Print a formatted comparison table"""
    print("\n" + "="*100)
    print("COMPREHENSIVE PERFORMANCE COMPARISON")
    print("="*100)
    print(f"{'Version':<15} {'Buy Sig':<8} {'Sell Sig':<9} {'Balance':<8} {'Avg Ret%':<10} {'Tot Ret%':<10} {'Win Rate%':<10} {'Max DD%':<10}")
    print("-"*100)
    
    for metrics in metrics_list:
        print(f"{metrics['label']:<15} {metrics['buy_signals']:<8} {metrics['sell_signals']:<9} {metrics['signal_balance']:<8} "
              f"{metrics['avg_return']:<10.2f} {metrics['total_return']:<10.2f} {metrics['win_rate']:<10.1f} {metrics['max_drawdown']:<10.2f}")

def main():
    """Main comparison function"""
    print("Comprehensive Signal Detection Comparison")
    print("=" * 50)
    
    # Load data
    df = load_data()
    if df is None:
        return
    
    # Run all versions
    print("\nRunning Original Logic...")
    buy_orig, sell_orig = run_original_logic(df.copy())
    metrics_orig = calculate_performance_metrics(buy_orig, sell_orig, "Original")
    
    print("Running Improved Logic...")
    buy_improved, sell_improved = run_improved_logic(df.copy())
    metrics_improved = calculate_performance_metrics(buy_improved, sell_improved, "Improved")
    
    print("Running Optimized Logic...")
    buy_optimized, sell_optimized = run_optimized_logic(df.copy())
    metrics_optimized = calculate_performance_metrics(buy_optimized, sell_optimized, "Optimized")
    
    # Print comparison
    print_comparison_table([metrics_orig, metrics_improved, metrics_optimized])
    
    # Recommendations
    print("\n" + "="*100)
    print("RECOMMENDATIONS")
    print("="*100)
    
    best_total_return = max(metrics_orig['total_return'], metrics_improved['total_return'], metrics_optimized['total_return'])
    best_avg_return = max(metrics_orig['avg_return'], metrics_improved['avg_return'], metrics_optimized['avg_return'])
    most_signals = max(metrics_orig['buy_signals'], metrics_improved['buy_signals'], metrics_optimized['buy_signals'])
    
    print(f"• Best Total Return: {best_total_return:.2f}%")
    print(f"• Best Average Return: {best_avg_return:.2f}%")
    print(f"• Most Signals Captured: {most_signals}")
    
    if metrics_optimized['total_return'] >= best_total_return * 0.95 and metrics_optimized['buy_signals'] > metrics_orig['buy_signals']:
        print("\n✓ RECOMMENDED: Use Optimized version - good balance of signal capture and quality")
    elif metrics_improved['buy_signals'] > metrics_orig['buy_signals'] * 1.5:
        print("\n✓ RECOMMENDED: Use Improved version - captures significantly more signals")
    else:
        print("\n✓ RECOMMENDED: Use Original version - highest quality signals")

if __name__ == "__main__":
    main()
