
# cleanup logic
BUY_018 = []
SELL_018 = []
b_list_018 = []
s_list_018 = []

position_018 = False

for idx, row in test_005.iterrows():
    i = test_005.index.get_loc(idx)
    
    # Ensure we are not at the first row to prevent index errors
    if i == 0:
        BUY_018.append(np.nan)
        b_list_018.append(0)
        SELL_018.append(np.nan)
        s_list_018.append(0)
        continue
    
    prev_row = test_005.iloc[i - 1]  # Get previous row for comparison

    if not position_018:
        # BUY conditions
        if (
            row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
            and row['P_PCT_017'] > 0
            and row['CLOSE'] > prev_row['CLOSE']
        ):
            BUY_018.append(row['CLOSE'])
            b_list_018.append(row['CLOSE'])
            SELL_018.append(np.nan)
            s_list_018.append(0)
            position_018 = True  # Now we hold a position

        elif (
            row['Flag_017'] == 'B' and prev_row['Flag_017'] == 'B'
            and row['Long_Min_017'] < -1
            and (row['P_PCT_017'] - row['Long_Min_017']) > 1.5
            and row['CLOSE'] > prev_row['CLOSE']
        ):
            BUY_018.append(row['CLOSE'])
            b_list_018.append(row['CLOSE'])
            SELL_018.append(np.nan)
            s_list_018.append(0)
            position_018 = True  # Entering a buy position

        else:
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(np.nan)
            s_list_018.append(0)

    else:  # If position_018 is True (we are holding a position)
        # SELL conditions
        if (
            row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
            and row['P_PCT_017'] > 0
            and row['CLOSE'] < prev_row['CLOSE']
        ):
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(row['CLOSE'])
            s_list_018.append(row['CLOSE'])
            position_018 = False  # Closing the position

        elif (
            row['Flag_017'] == 'S' and prev_row['Flag_017'] == 'S'
            and row['Short_Min_017'] < -1
            and (row['P_PCT_017'] - row['Short_Min_017']) > 1.5
            and row['CLOSE'] < prev_row['CLOSE']
        ):
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(row['CLOSE'])
            s_list_018.append(row['CLOSE'])
            position_018 = False  # Closing the position

        else:
            BUY_018.append(np.nan)
            b_list_018.append(0)
            SELL_018.append(np.nan)
            s_list_018.append(0)

# Assign results to DataFrame
test_005['Buy_S_P_018'] = BUY_018
test_005['Sell_S_P_018'] = SELL_018
test_005['Buy_F_018'] = b_list_018
test_005['Sell_F_018'] = s_list_018

