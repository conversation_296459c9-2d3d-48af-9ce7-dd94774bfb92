
# Improved cleanup logic with better signal detection
import numpy as np
import pandas as pd

# Initialize lists for signals
BUY_018 = []
SELL_018 = []
b_list_018 = []
s_list_018 = []

# Position tracking
position_018 = False
last_buy_price = None
last_sell_price = None

# Signal validation parameters
MIN_PRICE_CHANGE_THRESHOLD = 0.0001  # Minimum price change to consider valid
MIN_PCT_THRESHOLD = 0.1  # Minimum percentage threshold for signal validation
LOOKBACK_WINDOW = 3  # Number of periods to look back for signal confirmation

def validate_buy_signal(current_row, prev_row, position_state,
                       min_price_threshold=MIN_PRICE_CHANGE_THRESHOLD,
                       min_pct_threshold=MIN_PCT_THRESHOLD):
    """Enhanced buy signal validation with multiple conditions"""

    # Basic flag condition - must be B flag
    flag_condition = (current_row['Flag_017'] == 'B')

    # More flexible conditions for buy signals
    # Condition 1: Positive P_PCT with any price movement
    pct_positive = (current_row['P_PCT_017'] > 0)

    # Condition 2: Long recovery scenario
    long_recovery = (
        current_row['Long_Min_017'] < -1 and
        (current_row['P_PCT_017'] - current_row['Long_Min_017']) > 1.5
    )

    # Condition 3: Strong momentum (high P_PCT regardless of other factors)
    strong_momentum = (current_row['P_PCT_017'] > 1.0)

    # Condition 4: Flag transition from S to B (trend reversal)
    trend_reversal = (prev_row['Flag_017'] == 'S' and current_row['Flag_017'] == 'B')

    # Price validation - allow small negative moves for recovery scenarios
    price_ok = (
        current_row['CLOSE'] >= prev_row['CLOSE'] * 0.995 or  # Allow 0.5% negative move
        long_recovery or
        strong_momentum
    )

    return (
        flag_condition and
        (pct_positive or long_recovery or strong_momentum or trend_reversal) and
        price_ok and
        not position_state
    )

def validate_sell_signal(current_row, prev_row, position_state,
                        min_price_threshold=MIN_PRICE_CHANGE_THRESHOLD,
                        min_pct_threshold=MIN_PCT_THRESHOLD):
    """Enhanced sell signal validation with multiple conditions"""

    # Basic flag condition - must be S flag
    flag_condition = (current_row['Flag_017'] == 'S')

    # More flexible conditions for sell signals
    # Condition 1: Positive P_PCT with any price movement
    pct_positive = (current_row['P_PCT_017'] > 0)

    # Condition 2: Short recovery scenario
    short_recovery = (
        current_row['Short_Min_017'] < -1 and
        (current_row['P_PCT_017'] - current_row['Short_Min_017']) > 1.5
    )

    # Condition 3: Strong momentum (high P_PCT regardless of other factors)
    strong_momentum = (current_row['P_PCT_017'] > 1.0)

    # Condition 4: Flag transition from B to S (trend reversal)
    trend_reversal = (prev_row['Flag_017'] == 'B' and current_row['Flag_017'] == 'S')

    # Price validation - allow small positive moves for recovery scenarios
    price_ok = (
        current_row['CLOSE'] <= prev_row['CLOSE'] * 1.005 or  # Allow 0.5% positive move
        short_recovery or
        strong_momentum
    )

    return (
        flag_condition and
        (pct_positive or short_recovery or strong_momentum or trend_reversal) and
        price_ok and
        position_state
    )

# Main processing loop
for idx, row in test_005.iterrows():
    i = test_005.index.get_loc(idx)

    # Handle first row
    if i == 0:
        BUY_018.append(np.nan)
        b_list_018.append(0)
        SELL_018.append(np.nan)
        s_list_018.append(0)
        continue

    prev_row = test_005.iloc[i - 1]

    # Initialize default values
    buy_signal = np.nan
    sell_signal = np.nan
    buy_flag = 0
    sell_flag = 0

    # Check for buy signals when not in position
    if validate_buy_signal(row, prev_row, position_018):
        buy_signal = row['CLOSE']
        buy_flag = row['CLOSE']
        position_018 = True
        last_buy_price = row['CLOSE']

    # Check for sell signals when in position
    elif validate_sell_signal(row, prev_row, position_018):
        sell_signal = row['CLOSE']
        sell_flag = row['CLOSE']
        position_018 = False
        last_sell_price = row['CLOSE']

    # Append signals to lists
    BUY_018.append(buy_signal)
    b_list_018.append(buy_flag)
    SELL_018.append(sell_signal)
    s_list_018.append(sell_flag)

# Assign results to DataFrame
test_005['Buy_S_P_018'] = BUY_018
test_005['Sell_S_P_018'] = SELL_018
test_005['Buy_F_018'] = b_list_018
test_005['Sell_F_018'] = s_list_018

# Print summary statistics
buy_signals = sum(1 for x in BUY_018 if not pd.isna(x))
sell_signals = sum(1 for x in SELL_018 if not pd.isna(x))
print(f"Generated {buy_signals} buy signals and {sell_signals} sell signals")
print(f"Final position state: {'Long' if position_018 else 'Flat'}")

