#!/usr/bin/env python3
"""
Detailed analysis of signal detection patterns
"""

import pandas as pd
import numpy as np

def analyze_missed_signals():
    """Analyze where signals might be missed"""
    
    # Load data
    df = pd.read_csv('HIFIUSDT_20250913_14_02_45.csv')
    
    print("Detailed Signal Analysis")
    print("=" * 50)
    
    # Analyze flag patterns
    print("\nFlag Pattern Analysis:")
    flag_counts = df['Flag_017'].value_counts()
    print(flag_counts)
    
    # Look for potential signal opportunities
    potential_buys = 0
    potential_sells = 0
    
    print("\nPotential Signal Opportunities:")
    print("Row | Time | Flag | P_PCT | Long_Min | Short_Min | Close | Prev_Close | Opportunity")
    print("-" * 100)
    
    for i in range(1, min(100, len(df))):  # Analyze first 100 rows
        row = df.iloc[i]
        prev_row = df.iloc[i-1]
        
        # Check for potential buy opportunities
        if row['Flag_017'] == 'B':
            if (row['P_PCT_017'] > 0 or 
                (row['Long_Min_017'] < -1 and (row['P_PCT_017'] - row['Long_Min_017']) > 1.5)):
                potential_buys += 1
                opportunity = "BUY_OPPORTUNITY"
                print(f"{i:3d} | {row['TIME'][:16]} | {row['Flag_017']} | {row['P_PCT_017']:6.2f} | {row['Long_Min_017']:8.2f} | {row['Short_Min_017']:9.2f} | {row['CLOSE']:.5f} | {prev_row['CLOSE']:.5f} | {opportunity}")
        
        # Check for potential sell opportunities  
        elif row['Flag_017'] == 'S':
            if (row['P_PCT_017'] > 0 or 
                (row['Short_Min_017'] < -1 and (row['P_PCT_017'] - row['Short_Min_017']) > 1.5)):
                potential_sells += 1
                opportunity = "SELL_OPPORTUNITY"
                print(f"{i:3d} | {row['TIME'][:16]} | {row['Flag_017']} | {row['P_PCT_017']:6.2f} | {row['Long_Min_017']:8.2f} | {row['Short_Min_017']:9.2f} | {row['CLOSE']:.5f} | {prev_row['CLOSE']:.5f} | {opportunity}")
    
    print(f"\nPotential buy opportunities in first 100 rows: {potential_buys}")
    print(f"Potential sell opportunities in first 100 rows: {potential_sells}")
    
    # Analyze flag transitions
    print("\nFlag Transition Details:")
    transitions = []
    for i in range(1, len(df)):
        if df.iloc[i]['Flag_017'] != df.iloc[i-1]['Flag_017']:
            transitions.append({
                'row': i,
                'from': df.iloc[i-1]['Flag_017'],
                'to': df.iloc[i]['Flag_017'],
                'time': df.iloc[i]['TIME'],
                'price_change': df.iloc[i]['CLOSE'] - df.iloc[i-1]['CLOSE'],
                'pct_change': (df.iloc[i]['CLOSE'] - df.iloc[i-1]['CLOSE']) / df.iloc[i-1]['CLOSE'] * 100
            })
    
    print(f"Total transitions: {len(transitions)}")
    for t in transitions[:10]:  # Show first 10 transitions
        print(f"Row {t['row']}: {t['from']} -> {t['to']} at {t['time'][:16]}, Price change: {t['price_change']:.6f} ({t['pct_change']:.2f}%)")

if __name__ == "__main__":
    analyze_missed_signals()
