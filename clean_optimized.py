# Optimized cleanup logic with balanced signal detection
import numpy as np
import pandas as pd

# Initialize lists for signals
BUY_018 = []
SELL_018 = []
b_list_018 = []
s_list_018 = []

# Position tracking
position_018 = False
last_buy_price = None
last_sell_price = None

# Enhanced signal validation parameters
MIN_PRICE_CHANGE_THRESHOLD = 0.0001
MIN_PCT_THRESHOLD = 0.1
STRONG_SIGNAL_THRESHOLD = 1.0
RECOVERY_THRESHOLD = 1.5

def validate_buy_signal(current_row, prev_row, position_state,
                       strong_threshold=1.0, recovery_threshold=1.5, min_pct=0.1):
    """Optimized buy signal validation balancing capture rate and quality"""

    # Must be B flag
    if current_row['Flag_017'] != 'B' or position_state:
        return False

    # High-quality signals (prioritized)
    if current_row['P_PCT_017'] > strong_threshold:
        return True

    # Recovery signals
    if (current_row['Long_Min_017'] < -1 and
        (current_row['P_PCT_017'] - current_row['Long_Min_017']) > recovery_threshold):
        return True

    # Trend reversal signals (B flag after S flag)
    if prev_row['Flag_017'] == 'S' and current_row['P_PCT_017'] > 0:
        return True

    # Consecutive B signals with positive momentum
    if (prev_row['Flag_017'] == 'B' and
        current_row['P_PCT_017'] > 0 and
        current_row['CLOSE'] > prev_row['CLOSE']):
        return True

    # Medium strength signals with price confirmation
    if (current_row['P_PCT_017'] > min_pct and
        current_row['CLOSE'] >= prev_row['CLOSE'] * 0.999):
        return True

    return False

def validate_sell_signal(current_row, prev_row, position_state,
                        strong_threshold=1.0, recovery_threshold=1.5, min_pct=0.1):
    """Optimized sell signal validation balancing capture rate and quality"""

    # Must be S flag and in position
    if current_row['Flag_017'] != 'S' or not position_state:
        return False

    # High-quality signals (prioritized)
    if current_row['P_PCT_017'] > strong_threshold:
        return True

    # Recovery signals
    if (current_row['Short_Min_017'] < -1 and
        (current_row['P_PCT_017'] - current_row['Short_Min_017']) > recovery_threshold):
        return True

    # Trend reversal signals (S flag after B flag)
    if prev_row['Flag_017'] == 'B' and current_row['P_PCT_017'] > 0:
        return True

    # Consecutive S signals with negative momentum
    if (prev_row['Flag_017'] == 'S' and
        current_row['P_PCT_017'] > 0 and
        current_row['CLOSE'] < prev_row['CLOSE']):
        return True

    # Medium strength signals with price confirmation
    if (current_row['P_PCT_017'] > min_pct and
        current_row['CLOSE'] <= prev_row['CLOSE'] * 1.001):
        return True

    return False

def should_exit_position(current_row, prev_row, position_state, last_price):
    """Check for emergency exit conditions"""
    if not position_state or last_price is None:
        return False

    # Emergency exit on large adverse move
    if position_state and last_price:
        loss_pct = (current_row['CLOSE'] - last_price) / last_price * 100
        if loss_pct < -5:  # 5% stop loss
            return True

    return False

# Main processing loop
for idx, row in test_005.iterrows():
    i = test_005.index.get_loc(idx)
    
    # Handle first row
    if i == 0:
        BUY_018.append(np.nan)
        b_list_018.append(0)
        SELL_018.append(np.nan)
        s_list_018.append(0)
        continue
    
    prev_row = test_005.iloc[i - 1]
    
    # Initialize default values
    buy_signal = np.nan
    sell_signal = np.nan
    buy_flag = 0
    sell_flag = 0
    
    # Check for emergency exit first
    if should_exit_position(row, prev_row, position_018, last_buy_price):
        sell_signal = row['CLOSE']
        sell_flag = row['CLOSE']
        position_018 = False
        last_sell_price = row['CLOSE']
        
    # Check for buy signals when not in position
    elif validate_buy_signal(row, prev_row, position_018, STRONG_SIGNAL_THRESHOLD, RECOVERY_THRESHOLD, MIN_PCT_THRESHOLD):
        buy_signal = row['CLOSE']
        buy_flag = row['CLOSE']
        position_018 = True
        last_buy_price = row['CLOSE']

    # Check for sell signals when in position
    elif validate_sell_signal(row, prev_row, position_018, STRONG_SIGNAL_THRESHOLD, RECOVERY_THRESHOLD, MIN_PCT_THRESHOLD):
        sell_signal = row['CLOSE']
        sell_flag = row['CLOSE']
        position_018 = False
        last_sell_price = row['CLOSE']
    
    # Append signals to lists
    BUY_018.append(buy_signal)
    b_list_018.append(buy_flag)
    SELL_018.append(sell_signal)
    s_list_018.append(sell_flag)

# Assign results to DataFrame
test_005['Buy_S_P_018'] = BUY_018
test_005['Sell_S_P_018'] = SELL_018
test_005['Buy_F_018'] = b_list_018
test_005['Sell_F_018'] = s_list_018

# Print summary statistics
buy_signals = sum(1 for x in BUY_018 if not pd.isna(x))
sell_signals = sum(1 for x in SELL_018 if not pd.isna(x))
print(f"Generated {buy_signals} buy signals and {sell_signals} sell signals")
print(f"Final position state: {'Long' if position_018 else 'Flat'}")

# Calculate performance metrics
if buy_signals > 0 and sell_signals > 0:
    buy_prices = [x for x in BUY_018 if not pd.isna(x)]
    sell_prices = [x for x in SELL_018 if not pd.isna(x)]
    
    pairs = min(len(buy_prices), len(sell_prices))
    if pairs > 0:
        total_return = 0
        winning_trades = 0
        for i in range(pairs):
            if i < len(sell_prices):
                trade_return = (sell_prices[i] - buy_prices[i]) / buy_prices[i] * 100
                total_return += trade_return
                if trade_return > 0:
                    winning_trades += 1
        
        avg_return = total_return / pairs
        win_rate = winning_trades / pairs * 100
        print(f"Average return per trade: {avg_return:.2f}%")
        print(f"Total return: {total_return:.2f}%")
        print(f"Win rate: {win_rate:.1f}%")
